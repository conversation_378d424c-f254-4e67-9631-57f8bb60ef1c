package co.sandai.zeus.domain.plan.config;

import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ConfigInitializer implements SmartInitializingSingleton {

    @Autowired
    private PlanConfigService planConfigService;

    @Autowired
    private PlanConfigLoader planConfigLoader;

    @Override
    public void afterSingletonsInstantiated() {
        // 先执行 planConfigLoader 的初始化方法
        planConfigLoader.loadConfig();
    }
}
