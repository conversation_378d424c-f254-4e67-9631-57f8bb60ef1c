package co.sandai.zeus.domain.task.service;

import static co.sandai.zeus.domain.task.dao.TaskType.*;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.helper.M3U8Helper;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetMapper;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.common.CropArea;
import co.sandai.zeus.domain.task.dao.ModerationFailType;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskChunkStatus;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.TaskType;
import co.sandai.zeus.domain.task.dao.mapper.TaskChunkMapper;
import co.sandai.zeus.domain.task.dao.mapper.TaskMapper;
import co.sandai.zeus.infra.Green;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.infer.ArtifactClient;
import co.sandai.zeus.infra.infer.InferClient;
import co.sandai.zeus.infra.infer.dto.InferPipelineDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class TaskService {

    private final double CHUNK_FRAME_COUNT = 32;
    private final double CHUNK_FPS = 30;

    @Value("${athena.url}")
    private String athenaUrl;

    @Value("${athena.api.key}")
    private String athenaApiKey;

    @Autowired
    IDGenerator idGen;

    @Autowired
    TaskMapper taskMapper;

    @Autowired
    AssetService assetService;

    @Autowired
    private AssetMapper assetMapper;

    @Autowired
    private Green green;

    @Autowired
    private TaskChunkMapper taskChunkMapper;

    @Resource
    private ArtifactClient artifactClient;

    @Autowired
    private InferClient inferClient;

    @Resource
    private RestTemplate restTemplate;

    public byte[] mergeResultChunks(long taskId) {
        List<Asset> assets = assetService.getContinueChunkAssetByTaskId(taskId);
        return assetService.mergeTsList(assets);
    }

    public boolean allSafe(List<Long> taskIds) {
        int count = taskMapper.getUnSafeCount(taskIds);
        return count == 0;
    }

    private boolean needPreProcess(Task task) {
        if (task.getInspirationId() != null && task.getInspirationId() != 0) {
            return false;
        }
        if (task.isEnableAutoGenFirstFrame()) {
            return task.getType().equals(T2V);
        }
        if (task.isEnablePromptEnhancement()) {
            return true;
        }
        return Objects.nonNull(task.getCropArea());
    }

    public Task submitTask(Task task) {
        task = createTask(task);

        try {
            submitTaskAsAthenaJob(task);
            taskMapper.updateTaskStatus(task.getId(), TaskStatus.Running);
        } catch (IOException e) {
            throw new ZeusServiceException(ErrorCode.InternalError, e);
        }
        return task;
    }

    @Transactional
    public Task createTask(Task task) {
        // save task
        float duration = task.getDuration();
        int chunkCount = getChunkCountByDuration(duration);
        task.setChunkCount(chunkCount);
        task.setId(idGen.getNextId());
        task.setStatus(TaskStatus.Pending);

        if (task.getTaskSource() == null) {
            task.setTaskSource(TaskSourceEnum.NORMAL);
        }
        taskMapper.insertTask(task);
        return task;
    }

    public List<TaskChunkDO> getTaskChunkByTaskId(long taskId) {
        return taskChunkMapper.getByTaskId(taskId);
    }

    @Transactional
    public void cancelTask(Task task) {
        if (task.getStatus() == TaskStatus.Pending) {
            // TODO: call athena api service
            taskMapper.updateTaskStatus(task.getId(), TaskStatus.Canceled);
            log.info("task #{} cancelled", task.getId());
            return;
        }
        log.warn("task #{}, status: {}, can not be canceled", task.getId(), task.getStatus());
        throw new ZeusServiceException(ErrorCode.UnSupportedOperation, "Can not cancel this task");
    }

    public void deleteTaskById(long taskId) {
        taskMapper.deleteTaskById(taskId);
        log.info("task #{} deleted", taskId);
    }

    public int getChunkCountByDuration(float duration) {
        return (int) Math.ceil(duration / (CHUNK_FRAME_COUNT / CHUNK_FPS));
    }

    public float getChunkDuration() {
        return (float) (CHUNK_FRAME_COUNT / CHUNK_FPS);
    }

    public int getTaskCountByUserId(long userId) {
        return this.taskMapper.getTaskCountByUserId(userId);
    }

    public int getTaskCountByUser(List<Long> taskIds, long userId) {
        return this.taskMapper.getTaskCountOfUser(taskIds, userId);
    }

    public int getTaskCountByStatus(TaskStatus status) {
        return this.taskMapper.getTaskCountByStatus(status);
    }

    public Task getTaskById(long taskId) {
        return this.taskMapper.getTaskById(taskId);
    }

    @Deprecated
    public List<Task> getTasksByUserId(long userId, int offset, int limit, List<TaskSourceEnum> taskSourceEnumList) {
        return this.taskMapper.getTasksByUserId(userId, offset, limit, taskSourceEnumList);
    }

    /**
     * Get tasks by userId with multiple filters for both sources and types
     * @param userId User ID
     * @param offset Pagination offset
     * @param limit Pagination limit
     * @param taskSourceEnumList List of task sources to filter by
     * @param taskTypeList List of task types to filter by
     * @return List of tasks matching all criteria
     */
    public List<Task> getTasksByFilters(
            long userId, int offset, int limit, List<TaskSourceEnum> taskSourceEnumList, List<TaskType> taskTypeList) {
        return taskMapper.getTasksByUserIdAndFilters(userId, offset, limit, taskSourceEnumList, taskTypeList);
    }

    /**
     * Count tasks by userId with multiple filters for both sources and types
     * @param userId User ID
     * @param taskSourceEnumList List of task sources to filter by
     * @param taskTypeList List of task types to filter by
     * @return Count of tasks matching all criteria
     */
    public int countTasksByFilters(long userId, List<TaskSourceEnum> taskSourceEnumList, List<TaskType> taskTypeList) {
        return taskMapper.countTasksByUserIdAndFilters(userId, taskSourceEnumList, taskTypeList);
    }

    public List<Task> getTasksByStatus(
            TaskStatus status, int offset, int limit, List<TaskSourceEnum> taskSourceEnumList) {
        return this.taskMapper.getTasksByStatus(status, offset, limit, taskSourceEnumList);
    }

    public void updateTaskStatus(long taskId, TaskStatus status) {
        this.taskMapper.updateTaskStatus(taskId, status);
    }

    public void updateTaskSourceVideoId(Long taskId, Long videoId) {
        this.taskMapper.updateTaskSourceVideoId(taskId, videoId);
    }

    public void updateTaskModerationResult(long taskId, boolean passed, ModerationFailType failType, Float confidence) {
        taskMapper.updateTaskModerationResult(taskId, passed, failType, confidence);
    }

    private Asset saveTaskResult(Task task) {
        long userId = task.getUserId();
        long orgId = task.getOrgId();
        long taskId = task.getId();
        int width = task.getWidth();
        int height = task.getHeight();
        byte[] mp4Data = mergeResultChunks(task.getId());
        Asset asset = assetService.addAssetByBytes(
                mp4Data, userId, orgId, taskId, "mp4", AssetSource.Generate, 0, width, height);

        long taskResultPosterId = task.getResultVideoPosterId();
        if (taskResultPosterId > 0) {
            assetMapper.updateAssetPosterById(asset.getId(), taskResultPosterId);
        }

        taskMapper.updateTaskResultVideoId(taskId, asset.getId());
        log.info("save task {} result video to {}", taskId, asset.getOssPath());
        return asset;
    }

    public byte[] getTaskResultVideoBytes(Task task) {
        if (!task.getStatus().equals(TaskStatus.Success)) {
            throw new ZeusServiceException(ErrorCode.UnSupportedOperation, "Task is not completed.");
        }
        long resultVideoId = task.getResultVideoId();
        if (resultVideoId > 0) {
            return assetService.getAssetBytesById(resultVideoId);
        }
        Asset resultAsset = saveTaskResult(task);
        return assetService.getAssetBytes(resultAsset);
    }

    public byte[] getResultVideoBytesById(long taskId) {
        Task task = taskMapper.getTaskById(taskId);
        return getTaskResultVideoBytes(task);
    }

    public Asset getTaskResultVideoAsset(Task task) {
        if (!task.getStatus().equals(TaskStatus.Success)) {
            throw new ZeusServiceException(ErrorCode.UnSupportedOperation, "Task is not completed.");
        }
        long taskId = task.getId();
        long resultVideoId = task.getResultVideoId();

        // 已经合并 chunks
        if (resultVideoId > 0) {
            Asset resultVideoAsset = assetMapper.getAssetByIdWithDeleted(resultVideoId);
            long resultPosterId = resultVideoAsset.getPosterAssetId();

            // 结果 MP4 文件没有 poster
            if (resultPosterId == 0) {
                if (task.getResultVideoPosterId() > 0) {
                    // 使用任务 poster 更新
                    resultPosterId = task.getResultVideoPosterId();
                    assetMapper.updateAssetPosterById(resultVideoId, resultPosterId);
                    log.info("Update task result video {} poster with task poster: {}", resultVideoId, resultPosterId);
                } else {
                    // 重新生成 poster
                    InputStream stream = assetService.getAssetInputStreamById(resultVideoId);
                    Asset posterAsset =
                            assetService.addKeyFrameAsset(stream, task.getUserId(), task.getOrgId(), taskId);
                    assetService.saveAssetPoster(resultVideoAsset, posterAsset);
                    taskMapper.updateTaskPosterId(taskId, posterAsset.getId());
                    log.info("Generate task result poster: {} for task: {}", posterAsset.getId(), taskId);
                }
            }
            return resultVideoAsset;
        }

        return saveTaskResult(task);
    }

    public Asset getTaskResultVideoAssetById(long taskId) {
        Task task = taskMapper.getTaskById(taskId);
        if (Objects.isNull(task)) {
            throw ZeusServiceException.notFound(ErrorCode.TaskNotFound, "Task not found.");
        }
        return getTaskResultVideoAsset(task);
    }

    public byte[] getTaskM3U8FileContent(Task task) {
        List<Asset> assets = assetService.getContinueChunkAssetByTaskId(task.getId());
        List<String> chunkUrls = assetService.getAssetPublicUrls(assets, 60 * 24); // 1 day
        double chunkDuration = this.getChunkDuration();
        long targetDuration = (long) chunkDuration;
        return M3U8Helper.generateM3U8File(
                chunkUrls, chunkDuration, targetDuration, task.getStatus() == TaskStatus.Success);
    }

    public void fixNonMergedSuccessTask() {
        List<Task> tasks = taskMapper.getNonMergedSuccessTask();
        for (Task task : tasks) {
            getTaskResultVideoAsset(task);
        }
    }

    public List<TaskChunkDO> getTaskChunkByIds(List<Long> taskChunkIds) {
        return taskChunkMapper.getByIds(taskChunkIds);
    }

    @Setter
    @Getter
    public static class JobResponse {
        private String id;
        private String requestId;
    }

    public String submitTaskAsAthenaJob(Task task) throws IOException {
        // Initialize variables
        List<Asset> sourceAssets = Collections.emptyList();
        // Determine source assets based on task type
        if (task.getType().equals(TaskType.ExtendDuration)) {
            Asset sourceVideoAsset = null;
            if (task.getSourceVideoId() > 0) {
                sourceVideoAsset = assetService.getAssetById(task.getSourceVideoId());
            } else if (task.getSourceVideoTaskId() > 0) {
                sourceVideoAsset = getTaskResultVideoAsset(task);
            }

            if (sourceVideoAsset != null) {
                sourceAssets = Collections.singletonList(sourceVideoAsset);
            } else {
                throw new ZeusServiceException(
                        ErrorCode.InternalError,
                        "Source video asset is required for ExtendDuration task: " + task.getId());
            }
        } else if (task.getType().equals(TaskType.I2V)) {
            long sourceImageId = task.getSourceImageId();
            if (sourceImageId > 0) {
                Asset sourceImageAsset = assetService.getAssetById(sourceImageId);
                if (sourceImageAsset != null) {
                    sourceAssets = Collections.singletonList(sourceImageAsset);
                } else {
                    throw new ZeusServiceException(
                            ErrorCode.NotFound, "Source image asset not found for I2V task: " + task.getId());
                }
            } else {
                throw new ZeusServiceException(
                        ErrorCode.NotFound, "Source image ID is required for I2V task: " + task.getId());
            }
        } else if (task.getType().equals(T2V)) {
            // Text to Video doesn't need source assets
            log.info("Task type is T2V, no source assets needed");
        } else {
            // Handle any other task types
            log.info("Unhandled task type: {}, using empty source assets", task.getType());
        }

        // Create task chunk
        TaskChunkDO taskChunk = new TaskChunkDO();
        taskChunk.setTaskId(task.getId());
        taskChunk.setPrompt(task.getPrompt());
        taskChunk.setEnhancedPrompt(task.getEnhancedPrompt());
        taskChunk.setDuration(task.getDuration());
        taskChunk.setIndex(0);
        taskChunk.setStatus(TaskChunkStatus.Pending);

        log.info(
                "Creating task chunk for taskId: {}, prompt: {}, type: {}",
                task.getId(),
                taskChunk.getPrompt(),
                task.getType());

        //        taskChunkMapper.insert(taskChunk);
        List<TaskChunkDO> outputTaskChunks = Collections.singletonList(taskChunk);

        // Get enhancement type from extraInferArgs if available
        TaskExtraInferArgs inferArgs = task.getExtraInferArgs();
        String enhancementType = "";
        if (Objects.nonNull(inferArgs)) {
            enhancementType = inferArgs.getEnhancementType();
        }

        // Submit task to Athena job
        return submitTaskAsAthenaJob(task, sourceAssets, outputTaskChunks, enhancementType);
    }

    public String submitTaskAsAthenaJob(
            Task task, List<Asset> sourceAssets, List<TaskChunkDO> outputTaskChunks, String enhancementType)
            throws IOException {
        return submitTaskAsAthenaJob(task, sourceAssets, outputTaskChunks, enhancementType, null);
    }

    public String submitTaskAsAthenaJob(
            Task task,
            List<Asset> sourceAssets,
            List<TaskChunkDO> outputTaskChunks,
            String enhancementType,
            String queue)
            throws IOException {
        return submitTaskAsAthenaJob(task, sourceAssets, outputTaskChunks, enhancementType, queue, null);
    }

    /**
     *
     * @param task
     * @param sourceAssets
     * @param steps
     * @param enhancementType
     * @param queue
     * @param allPrompts 为null时表示审核task中自己的prompt，为集合时表示审核该集合中的prompt，包括空集合（表现为跳过审核）
     * @throws IOException
     */
    public String submitTaskAsAthenaJob(
            Task task,
            List<Asset> sourceAssets,
            List<TaskChunkDO> steps,
            String enhancementType,
            String queue,
            List<String> allPrompts)
            throws IOException {
        // Prepare request body
        InferPipelineDTO requestBody =
                buildAthenaPipelineRequestBody(task, sourceAssets, steps, enhancementType, queue);

        ResponseEntity<String> response = inferClient.pushToInferJob(requestBody);

        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Failed to push job. Status: {}, Response: {}", response.getStatusCode(), response.getBody());
            throw new IOException("Failed to push job: " + response.getStatusCode());
        }
        JobResponse jobResponse = parseJobResponse(response.getBody());
        return jobResponse.getId();
    }

    /**
     * 审核任务提示词并处理审核结果
     * 该方法不在事务内执行，不会导致任务创建事务回滚
     *
     * @param taskId 任务 ID
     * @param jobId Athena任务ID
     * @param promptToModerate 要审核的提示词
     * @return 审核是否通过
     */
    public boolean moderateTaskPrompt(Long taskId, Long userId, Long orgId, String jobId, String promptToModerate) {
        if (StringUtils.isBlank(promptToModerate)) {
            LogUtil.warnf(log, "prompt is empty, skip moderation");
            return true;
        }
        // todo 2025/4/23 待优化项： 如果prompt过长，分段审核； 否则可能导致多个pipeline+长prompt时后续的prompt可以跳过审核
        Green.ModerateTextResult result;
        Long accountId = userId;
        if (Objects.isNull(accountId)) {
            accountId = orgId;
        }
        try {
            // 文本内容审核
            result = green.moderateText(promptToModerate, accountId);
        } catch (Exception e) {
            log.error("Failed to moderate task {}: {}", taskId, e.getMessage(), e);
            return false;
        }

        if (!result.isValid()) {
            // 记录文本审核结果的置信度和失败原因
            Float confidence = result.getConfidence();
            String reason = result.getReason();
            ModerationFailType failType = ModerationFailType.PROMPT;

            log.warn(
                    "user prompt moderation is not passed, confidence: {}, reason: {}, prompt: {}",
                    confidence,
                    reason,
                    promptToModerate);

            // 取消推理任务
            inferClient.cancelJob(jobId);

            // 更新数据库中的任务状态和审核信息
            taskMapper.updateTaskStatus(taskId, TaskStatus.Canceled);
            taskMapper.updateTaskModerationResult(taskId, false, failType, confidence);

            return false;
        }
        return true;
    }

    private InferPipelineDTO buildAthenaPipelineRequestBody(
            Task task, List<Asset> sourceAssets, List<TaskChunkDO> steps, String enhancementType, String queue)
            throws IOException {
        InferPipelineDTO.OptionsDTO options = buildAthenaPipelineOptions(task);
        options.setPromptEnhancementType(enhancementType);
        // Handle artifact uploads
        List<InferPipelineDTO.InputDTO> inputs = new ArrayList<>();
        for (Asset asset : sourceAssets) {
            String name;
            String fileExtension;
            if (asset.getMediaType().isVideo()) {
                name = "video";
                fileExtension = ".mp4";
            } else {
                name = "image";
                // 这里就写死是jpeg了？？
                fileExtension = ".jpeg";
            }

            if (asset.getUrn() == null || asset.getUrn().isEmpty()) {
                byte[] data = assetService.getAssetBytes(asset);
                ArtifactClient.ArtifactUploadResponseDTO video =
                        artifactClient.uploadArtifact(name, fileExtension, data);
                asset.setUrn(video.urn());
                assetService.updateUrn(asset.getId(), video.urn());
            }
            inputs.add(new InferPipelineDTO.InputDTO().setName(name).setUrn(asset.getUrn()));
        }
        List<InferPipelineDTO.StepDTO> stepsDTO = new ArrayList<>();
        for (TaskChunkDO taskChunkDO : steps) {
            InferPipelineDTO.StepDTO stepDTO = new InferPipelineDTO.StepDTO()
                    .setDuration(taskChunkDO.getDurationByTaskType(task.getType()))
                    .setPrompt(
                            StringUtils.isNotBlank(taskChunkDO.getEnhancedPrompt())
                                    ? taskChunkDO.getEnhancedPrompt()
                                    : taskChunkDO.getPrompt());
            if (taskChunkDO.getAudioAssetId() != null) {
                Asset asset = assetService.getAssetById(taskChunkDO.getAudioAssetId());
                stepDTO.setAudioUrn(asset.getUrn());
                if (taskChunkDO.getDuration() == null) {
                    stepDTO.setDuration(asset.getDuration());
                }
                // TODO 推理侧按照sources中实现，先跑通整个流程
                inputs.add(new InferPipelineDTO.InputDTO().setName("audio").setUrn(asset.getUrn()));
            }
            stepsDTO.add(stepDTO);
        }
        InferPipelineDTO requestBody = new InferPipelineDTO();
        requestBody.setOptions(options);
        requestBody.setInputs(inputs);
        requestBody.setModel(task.getModel());
        requestBody.setSteps(stepsDTO);
        requestBody.setType(task.getType().name());
        requestBody.setQueue(StringUtils.isBlank(queue) ? task.getTaskSource().getQueue() : queue);
        requestBody.setRequestId(String.valueOf(task.getId()));

        // Set model version from task extra infer args
        TaskExtraInferArgs extraInferArgs = task.getExtraInferArgs();
        if (extraInferArgs != null && extraInferArgs.getModelVersion() != null) {
            requestBody.setModelVersion(extraInferArgs.getModelVersion());
        }

        return requestBody;
    }

    private InferPipelineDTO.OptionsDTO buildAthenaPipelineOptions(Task task) {
        List<String> specialTokens = new ArrayList<>();
        String vaeModel = "";
        String resolution = "";
        TaskExtraInferArgs inferArgs = task.getExtraInferArgs();
        int nSampleSteps = 0;
        String extra = "";
        boolean enableWatermark = false;
        boolean enableInputVideoToTs = false;
        boolean dryRun = inferArgs.isDryRun();
        if (Objects.nonNull(inferArgs)) {
            specialTokens = inferArgs.getSpecialTokens();
            vaeModel = inferArgs.getVaeModel();
            resolution = inferArgs.getResolution();
            nSampleSteps = inferArgs.getNSampleSteps();
            extra = inferArgs.getExtra();
            enableWatermark = inferArgs.isEnableWatermark();
            enableInputVideoToTs = inferArgs.isEnableInputVideoToTs();
        }

        // Convert proto message to request format
        InferPipelineDTO.OptionsDTO options = new InferPipelineDTO.OptionsDTO();
        options.setTSchedulerFunc(task.getTSchedulerFunc());
        options.setTSchedulerFuncArgs(task.getTSchedulerArgs());
        options.setSeed(task.getSeed());
        options.setAspectRatio(task.getAspectRatio());
        options.setSpecialTokens(specialTokens);
        options.setVaeModel(vaeModel);
        options.setResolution(resolution);
        options.setPromptEnhancement(task.isEnablePromptEnhancement());
        options.setNSampleSteps(nSampleSteps);
        options.setCropTimeStart(task.getSourceVideoStartTimestamp());
        options.setCropTimeEnd(task.getSourceVideoEndTimestamp());
        options.setDryRun(dryRun);
        options.setEnableWatermark(enableWatermark);
        options.setEnableInputVideoToTs(enableInputVideoToTs);

        CropArea cropArea = task.getCropArea();
        if (cropArea != null) {
            Map<String, Integer> cropAreaMap = CropArea.convertToMap(cropArea);
            options.setCropArea(cropAreaMap);
        }

        Map<String, Object> extraMap = new HashMap<>();
        if (!extra.isEmpty()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                extraMap = objectMapper.readValue(extra, new TypeReference<>() {});
                log.debug("Parsed extra options: {}", extraMap);
            } catch (Exception e) {
                log.error("Failed to parse extra json: {}", extra, e);
            }
            options.setExtra(extraMap);
        }

        return options;
    }

    private JobResponse parseJobResponse(String responseBody) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode responseJson = mapper.readTree(responseBody);
        JsonNode data = responseJson.get("data");

        log.info(
                "Task published with ID: {}, Request ID: {}",
                data.get("id").asText(),
                data.get("request_id").asText());

        JobResponse jobResponse = new JobResponse();
        jobResponse.setId(data.get("id").asText());
        jobResponse.setRequestId(data.get("request_id").asText());
        return jobResponse;
    }
}
