package co.sandai.zeus.domain.favorite.service;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetMediaType;
import co.sandai.zeus.domain.asset.dto.AssetFilterParam;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.favorite.dao.AssetFavorite;
import co.sandai.zeus.domain.favorite.dao.AssetFavoriteMapper;
import co.sandai.zeus.infra.IDGenerator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AssetFavoriteService {
    @Autowired
    private AssetFavoriteMapper assetFavoriteMapper;

    @Autowired
    private IDGenerator idGenerator;

    @Autowired
    private AssetService assetService;

    public long addAssetFavorite(long userId, long assetId, AssetMediaType mediaType) {
        AssetFavorite favorite = AssetFavorite.builder()
                .id(idGenerator.getNextId())
                .userId(userId)
                .assetId(assetId)
                .mediaType(mediaType.name())
                .build();

        try {
            assetFavoriteMapper.insertAssetFavorite(favorite);
        } catch (DuplicateKeyException ex) {
            log.warn("Duplicate favorite for user {} and asset {}", userId, assetId, ex);
            throw new ZeusServiceException(
                    HttpStatus.BAD_REQUEST, ErrorCode.DuplicateFavorite, "You have already favorite this asset");
        }
        log.info("User {} added asset {} to favorites", userId, assetId);
        return favorite.getAssetId();
    }

    public void removeAssetFavorite(long userId, long assetId) {
        int rows = assetFavoriteMapper.deleteUserFavoriteAsset(userId, assetId);
        if (rows > 0) {
            log.info("User {} removed asset {} from favorites", userId, assetId);
        }
    }

    // 支持过滤types
    public List<Asset> getUserFavoriteAssets(long userId, AssetFilterParam filterParam, int limit, int offset) {
        List<AssetFavorite> favorites = assetFavoriteMapper.getUserFavoriteAssets(
                userId, filterParam != null ? filterParam.getTypes() : null, limit, offset);
        return assetService.getAssetsByIds(
                favorites.stream().map(AssetFavorite::getAssetId).toList());
    }

    public int getFavoriteAssetsCount(long userId, AssetFilterParam filterParam) {
        return assetFavoriteMapper.getFavoriteAssetCount(userId, filterParam != null ? filterParam.getTypes() : null);
    }

    public Map<Long, Boolean> getFavoriteAssetStatusMap(long userId, List<Long> assetIds) {
        if (assetIds == null || assetIds.isEmpty()) {
            return Map.of();
        }
        List<Long> favoriteAssetIds = assetFavoriteMapper.getFavoriteAssetIdsByAssetIds(userId, assetIds);
        return assetIds.stream().collect(Collectors.toMap(id -> id, favoriteAssetIds::contains));
    }
}
