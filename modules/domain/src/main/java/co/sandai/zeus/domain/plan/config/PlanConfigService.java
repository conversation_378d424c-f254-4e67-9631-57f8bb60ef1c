package co.sandai.zeus.domain.plan.config;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.domain.plan.model.Auth;
import co.sandai.zeus.domain.plan.model.MemberShip;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import co.sandai.zeus.domain.platform.credit.PlatformCreditService;
import java.util.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 计划配置服务
 * <p>
 * 该类负责提供计划、会员和价格模型的相关信息。
 * </p>
 * 依赖planConfigLoader加载器的loadConfig方法先执行, 再执行本类的init方法，因此用ConfigInitializer类来保证加载顺序。
 */
@Service
@Slf4j
public class PlanConfigService {

    private List<Plan> planList;

    private List<MemberShip> memberShipList;

    private Map<String, Auth> authMap;

    private Map<String, PriceModel> priceModelMap;

    @Getter
    private final int freeCreditAmount = 500;

    @Autowired
    private PlatformCreditService platformCreditService;

    void setPlanMetaData(
            List<Plan> planList,
            List<MemberShip> memberShipList,
            Map<String, Auth> authMap,
            Map<String, PriceModel> priceModelMap) {
        this.planList = planList;
        this.memberShipList = memberShipList;
        this.authMap = authMap;
        this.priceModelMap = priceModelMap;
    }

    public Plan getFreePlan() {
        return this.planList.getFirst();
    }

    public MemberShip getFreeMemberShip() {
        return this.memberShipList.getFirst();
    }

    public PriceModel getFreePriceModel() {
        // 没有 outPriceId 的为 free price
        return priceModelMap.get("");
    }

    public PriceModel getPriceModelByPriceId(String priceId) {
        PriceModel priceModel = priceModelMap.get(priceId);
        if (Objects.isNull(priceModel)) {
            priceModel = platformCreditService.getPriceModelByPriceId(priceId);
            if (Objects.nonNull(priceModel)) {
                log.info("found platform price model, id: {}", priceId);
            }
        }
        if (priceModel == null) {
            LogUtil.warnf(log, "priceModel not found, priceId: {0}", priceId);
            return null;
        }
        return priceModel;
    }

    /**
     * @param planStatusEnum 为空表示不对status进行过滤
     */
    public Plan getPlanByPriceId(String priceId, PlanStatusEnum planStatusEnum) {
        PriceModel priceModel = priceModelMap.get(priceId);
        if (priceModel == null) {
            LogUtil.warnf(log, "priceModel not found, priceId: {0}", priceId);
            return null;
        }
        if (priceModel.getProductType() != PriceModel.ProductTypeEnum.PLAN) {
            LogUtil.warnf(log, "the priceId is not from a plan: {0}", priceId);
            return null;
        }
        return planList.stream()
                .filter(plan -> planStatusEnum == null || plan.getStatus() == planStatusEnum)
                .filter(plan -> plan.getPlanCode().equals(priceModel.getOutCode()))
                .findFirst()
                .orElse(null);
    }

    public Plan getPlanByCode(String planCode, PlanStatusEnum planStatusEnum) {
        return planList.stream()
                .filter(plan -> planStatusEnum == null || plan.getStatus() == planStatusEnum)
                .filter(plan -> plan.getPlanCode().equals(planCode))
                .findFirst()
                .orElse(null);
    }

    public List<Plan> getActivePlanList() {
        return planList.stream()
                .filter(plan -> plan.getStatus() == PlanStatusEnum.ACTIVE)
                .toList();
    }

    public List<Plan> getUsablePlanList() {
        return planList.stream()
                .filter(plan ->
                        plan.getStatus() == PlanStatusEnum.ACTIVE || plan.getStatus() == PlanStatusEnum.DEPRECATED)
                .toList();
    }

    public PriceModel getPriceModelOfPlan(String planCode, String currency) {
        for (PriceModel priceModel : priceModelMap.values()) {
            if (planCode.equals(priceModel.getOutCode())
                    && currency.equals(priceModel.getCurrency().getCurrencyCode())
                    && priceModel.getProductType() == PriceModel.ProductTypeEnum.PLAN) {
                return priceModel;
            }
        }
        return null;
    }

    public PriceModel getPriceModelOfCredit(String creditPackageCode) {
        for (PriceModel priceModel : priceModelMap.values()) {
            if (creditPackageCode.equals(priceModel.getOutCode())
                    && priceModel.getProductType() == PriceModel.ProductTypeEnum.CREDIT) {
                return priceModel;
            }
        }
        return null;
    }

    public Auth queryAuth(String authCode) {
        return authMap.get(authCode);
    }
}
