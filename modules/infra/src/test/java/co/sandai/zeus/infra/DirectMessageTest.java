package co.sandai.zeus.infra;

import static org.junit.jupiter.api.Assertions.assertThrows;

import java.io.IOException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = {DirectMessage.class})
public class DirectMessageTest {

    @Autowired
    private DirectMessage dm;

    @Test
    public void testSendMessage() throws IOException {
        dm.sendEmail("<EMAIL>", "Test", "hello world");
    }

    @Test
    public void testSendMessageWithInvalidEmail() {
        assertThrows(RuntimeException.class, () -> {
            dm.sendEmail("invalid-email", "Test", "hello world");
        });
    }
}
