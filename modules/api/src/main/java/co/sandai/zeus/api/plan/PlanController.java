package co.sandai.zeus.api.plan;

import co.sandai.zeus.api.plan.view.PlanPageVo;
import co.sandai.zeus.api.plan.view.UserPlanVo;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.EmptySuccessResponse;
import co.sandai.zeus.domain.plan.UserPlanService;
import co.sandai.zeus.domain.plan.bo.UserPlanDetailBO;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/plans")
public class PlanController {

    @Resource
    private PlanConfigService planConfigService;

    @Resource
    private UserApiService userApiService;

    @Resource
    private UserPlanService userPlanService;

    /**
     * 返回所有活跃的计划（包含免费计划）
     */
    @GetMapping("")
    @PrintLog
    public PlanPageVo queryActivePlans() {
        // 添加其他活跃计划
        List<Plan> activePlanList = planConfigService.getActivePlanList();
        List<PlanVo> voList = activePlanList.stream()
                .map(plan -> {
                    // 先默认返回usd价格的显示
                    PriceModel priceModel = planConfigService.getPriceModelOfPlan(plan.getPlanCode(), "USD");
                    return PlanVo.convertFromModel(plan, priceModel);
                })
                .toList();

        PlanPageVo planPageVo = new PlanPageVo();
        planPageVo.setPlanList(voList);
        planPageVo.setFreeCreditAmount(planConfigService.getFreeCreditAmount());
        return planPageVo;
    }

    @GetMapping("/user")
    @PrintLog
    public UserPlanVo queryUserPlan() {
        User currentUser = userApiService.getCurrentUser();
        UserPlanDetailBO userPlanDetailBO = userPlanService.queryUserPlanDetail(currentUser.getId());
        Plan plan;
        PriceModel priceModel;
        if (userPlanDetailBO == null) {
            // 默认为free计划
            plan = planConfigService.getFreePlan();
            priceModel = planConfigService.getFreePriceModel();
        } else {
            plan = planConfigService.getPlanByCode(userPlanDetailBO.getPlanCode(), PlanStatusEnum.ACTIVE);
            priceModel = planConfigService.getPriceModelOfPlan(plan.getPlanCode(), "USD");
        }
        return UserPlanVo.convertFromModel(userPlanDetailBO, plan, priceModel);
    }

    @PutMapping("/user/{priceId}/cancel")
    @PrintLog
    public EmptySuccessResponse cancelUserPlan(@PathVariable String priceId) {
        User currentUser = userApiService.getCurrentUser();
        userPlanService.cancelSubscriptionWithoutRefund(currentUser, priceId);
        return EmptySuccessResponse.builder().build();
    }
}
