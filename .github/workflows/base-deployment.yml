on:
  workflow_call:
    inputs:
      env:
        required: true
        type: string
      envUrl:
        required: true
        type: string
      cluster_id:
        required: true
        type: string
      image_tag:
        required: true
        type: string
      deploy_zeus:
        description: '部署 Zeus'
        type: boolean
        default: true
      deploy_zeus_gaga:
        description: '部署 Zeus GaGa'
        type: boolean
        default: true
      deploy_platform:
        description: '部署 Platform'
        type: boolean
        default: true
      deploy_openapi:
        description: '部署 OpenAPI'
        type: boolean
        default: false
    secrets:
      aliyun_ak:
        required: true
      aliyun_sk:
        required: true
      feishu_webhook_url:
        required: true

env:
  REGION_ID: cn-hongkong
  TARGET_ENV: ${{ inputs.env }}

  ACK_CLUSTER_ID: ${{ inputs.cluster_id }}
  ACK_DEPLOYMENT_NAME: ${{ inputs.env }}-zeus-deployment
  ACK_PLATFORM_DEPLOYMENT_NAME: ${{ inputs.env }}-platform-zeus-deployment
  ACK_OPENAPI_DEPLOYMENT_NAME: ${{ inputs.env }}-openapi-zeus-deployment

  ACR_EE_REGISTRY: ${{ inputs.env == 'prod' && 'sandai-registry.cn-hongkong.cr.aliyuncs.com' || 'sandai-registry-vpc.cn-hongkong.cr.aliyuncs.com' }}
  ACR_EE_INSTANCE_ID: cri-ygtpwto064tjuv5o
  ACR_EE_NAMESPACE: sandai-product
  ACR_EE_IMAGE: zeus
  ACR_EE_OPENAPI_IMAGE: zeus-openapi
  ACR_EE_TAG: ${{ inputs.image_tag }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

permissions:
  contents: read

jobs:
  deploy_zeus:
    runs-on: ubuntu-latest
    if: ${{ inputs.deploy_zeus }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set K8s context
        uses: aliyun/ack-set-context@v1
        with:
          access-key-id: "${{ secrets.aliyun_ak }}"
          access-key-secret: "${{ secrets.aliyun_sk }}"
          cluster-id: "${{ env.ACK_CLUSTER_ID }}"

      - name: Set up Kustomize
        run: |-
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash

      - name: Deploy
        run: |-
          cd ./deployment/overlays/zeus-$TARGET_ENV
          ../../../kustomize edit set image REGISTRY/NAMESPACE/IMAGE:TAG=$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_IMAGE:$ACR_EE_TAG
          ../../../kustomize build | kubectl apply -f -
          kubectl rollout status deployment/$ACK_DEPLOYMENT_NAME
          kubectl get services -o wide

      - name: OnSuccess
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus 部署成功",
                  "theme": "green",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

      - name: OnFailure
        if: ${{ failure() }}
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus 部署失败",
                  "theme": "red",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

  deploy_zeus_gaga:
    runs-on: ubuntu-latest
    if: ${{ inputs.deploy_zeus_gaga }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set K8s context
        uses: aliyun/ack-set-context@v1
        with:
          access-key-id: "${{ secrets.aliyun_ak }}"
          access-key-secret: "${{ secrets.aliyun_sk }}"
          cluster-id: "${{ env.ACK_CLUSTER_ID }}"

      - name: Set up Kustomize
        run: |-
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash

      - name: Deploy
        run: |-
          cd ./deployment/overlays/zeus-$TARGET_ENV
          ../../../kustomize edit set image REGISTRY/NAMESPACE/IMAGE:TAG=$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_IMAGE:$ACR_EE_TAG
          ../../../kustomize build | kubectl apply -f -
          kubectl rollout status deployment/$ACK_DEPLOYMENT_NAME
          kubectl get services -o wide

      - name: OnSuccess
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus GaGa 部署成功",
                  "theme": "green",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

      - name: OnFailure
        if: ${{ failure() }}
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus GaGa 部署失败",
                  "theme": "red",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

  deploy_platform:
    runs-on: ubuntu-latest
    if: ${{ inputs.deploy_platform }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set K8s context
        uses: aliyun/ack-set-context@v1
        with:
          access-key-id: "${{ secrets.aliyun_ak }}"
          access-key-secret: "${{ secrets.aliyun_sk }}"
          cluster-id: "${{ env.ACK_CLUSTER_ID }}"

      - name: Set up Kustomize
        run: |-
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash

      - name: Deploy
        run: |-
          cd ./deployment/overlays/zeus-platform-$TARGET_ENV
          ../../../kustomize edit set image REGISTRY/NAMESPACE/IMAGE:TAG=$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_IMAGE:$ACR_EE_TAG
          ../../../kustomize build | kubectl apply -f -
          kubectl rollout status deployment/$ACK_PLATFORM_DEPLOYMENT_NAME
          kubectl get services -o wide

      - name: OnSuccess
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus Platform 部署成功 (${{ github.ref_name }})",
                  "theme": "green",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

      - name: OnFailure
        if: ${{ failure() }}
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus Platform 部署失败 (${{ github.ref_name }})",
                  "theme": "red",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

  deploy_openapi:
    runs-on: ubuntu-latest
    if: ${{ inputs.deploy_openapi }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set K8s context
        uses: aliyun/ack-set-context@v1
        with:
          access-key-id: "${{ secrets.aliyun_ak }}"
          access-key-secret: "${{ secrets.aliyun_sk }}"
          cluster-id: "${{ env.ACK_CLUSTER_ID }}"

      - name: Set up Kustomize
        run: |-
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash

      - name: Deploy
        run: |-
          cd ./deployment/overlays/zeus-openapi-$TARGET_ENV
          ../../../kustomize edit set image REGISTRY/NAMESPACE/IMAGE:TAG=$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_OPENAPI_IMAGE:$ACR_EE_TAG
          ../../../kustomize build | kubectl apply -f -
          kubectl rollout status deployment/$ACK_OPENAPI_DEPLOYMENT_NAME
          kubectl get services -o wide

      - name: OnSuccess
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus OpenAPI 部署成功 (${{ github.ref_name }})",
                  "theme": "green",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_OPENAPI_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

      - name: OnFailure
        if: ${{ failure() }}
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus OpenAPI 部署失败 (${{ github.ref_name }})",
                  "theme": "red",
                  "content": "部署环境：${{ env.TARGET_ENV }}\n部署镜像：${{env.ACR_EE_OPENAPI_IMAGE}}:${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'