name: CI/CD

on:
  push:
    branches:
      - dev
  workflow_dispatch:
    inputs:
      build_zeus:
        description: '构建 Zeus 镜像'
        type: boolean
        default: true
      build_openapi:
        description: '构建 OpenAPI 镜像'
        type: boolean
        default: false
      env:
        description: '目标环境'
        type: string
        default: dev
      deploy_zeus_gaga:
        description: '部署 Zeus GaGa API'
        type: boolean
        default: true
      deploy_zeus:
        description: '部署 Zeus Magi API'
        type: boolean
        default: true
      deploy_platform:
        description: '部署 Platform'
        type: boolean
        default: true
      deploy_openapi:
        description: '部署 OpenAPI'
        type: boolean
        default: false

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    uses: ./.github/workflows/base-build.yml
    with:
      build_zeus: ${{ github.event_name == 'workflow_dispatch' && inputs.build_zeus || true }}
      build_openapi: ${{ github.event_name == 'workflow_dispatch' && inputs.build_openapi || true }}
    secrets:
      aliyun_ak: "${{ secrets.ALIYUN_ACCESS_KEY_ID }}"
      aliyun_sk: "${{ secrets.ALIYUN_ACCESS_KEY_SECRET }}"
      feishu_webhook_url: "${{ secrets.FEISHU_WEBHOOK_URL }}"

  deploy:
    needs: build
    uses: ./.github/workflows/base-deployment.yml
    with:
      env: ${{ github.event_name == 'workflow_dispatch' && inputs.env || 'dev' }}
      envUrl: https://magi-dev.sandaii.cn
      cluster_id: c4d615674e9994747af45004405e35503
      image_tag: ${{ github.sha }}
      deploy_zeus: ${{ github.event_name == 'workflow_dispatch' && inputs.deploy_zeus || true }}
      deploy_zeus_gaga: ${{ github.event_name == 'workflow_dispatch' && inputs.deploy_zeus_gaga || true }}
      deploy_openapi: ${{ github.event_name == 'workflow_dispatch' && inputs.deploy_openapi || true }}
      deploy_platform: ${{ github.event_name == 'workflow_dispatch' && inputs.deploy_platform || true }}
    secrets:
      aliyun_ak: "${{ secrets.ALIYUN_ACCESS_KEY_ID }}"
      aliyun_sk: "${{ secrets.ALIYUN_ACCESS_KEY_SECRET }}"
      feishu_webhook_url: "${{ secrets.FEISHU_WEBHOOK_URL }}"
