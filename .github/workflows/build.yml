name: 镜像构建

on:
  workflow_dispatch:
    inputs:
      build_zeus:
        description: '构建 Zeus 镜像'
        type: boolean
        default: true
      build_openapi:
        description: '构建 OpenAPI 镜像'
        type: boolean
        default: false

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    uses: ./.github/workflows/base-build.yml
    with:
      build_zeus: ${{inputs.build_zeus}}
      build_openapi: ${{inputs.build_openapi}}
    secrets:
      aliyun_ak: "${{ secrets.ALIYUN_ACCESS_KEY_ID }}"
      aliyun_sk: "${{ secrets.ALIYUN_ACCESS_KEY_SECRET }}"
      feishu_webhook_url: "${{ secrets.FEISHU_WEBHOOK_URL }}"

