on:
  workflow_call:
    inputs:
      build_zeus:
        description: '构建 Zeus 镜像'
        type: boolean
        default: true
      build_openapi:
        description: '构建 OpenAPI 镜像'
        type: boolean
        default: false
    secrets:
      aliyun_ak:
        required: true
      aliyun_sk:
        required: true
      feishu_webhook_url:
        required: true

env:
  REGION_ID: cn-hongkong

  ACK_CLUSTER_ID: c4d615674e9994747af45004405e35503

  ACR_EE_REGISTRY: sandai-registry.cn-hongkong.cr.aliyuncs.com
  ACR_EE_INSTANCE_ID: cri-ygtpwto064tjuv5o
  ACR_EE_NAMESPACE: sandai-product
  ACR_EE_IMAGE: zeus
  ACR_EE_OPENAPI_IMAGE: zeus-openapi
  ACR_EE_TAG: ${{ github.sha }}

permissions:
  contents: read

jobs:
  build-zeus:
    if: ${{ inputs.build_zeus == true }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to ACR EE with the AccessKey pair
        uses: aliyun/acr-login@v1
        with:
          login-server: "https://${{ env.ACR_EE_REGISTRY }}"
          region-id: "${{ env.REGION_ID }}"
          access-key-id: "${{ secrets.aliyun_ak }}"
          access-key-secret: "${{ secrets.aliyun_sk}}"
          instance-id: "${{ env.ACR_EE_INSTANCE_ID }}"

      - name: Build and push image to ACR EE
        run: |
          docker build \
            -t "$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_IMAGE:$ACR_EE_TAG" .
          docker push "$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_IMAGE:$ACR_EE_TAG"

      - name: OnSuccess
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus 构建成功 (${{ github.ref_name }})",
                  "theme": "green",
                  "content": "构建镜像：${{ env.ACR_EE_TAG }}",
                  "btnText": "前往部署",
                  "detailUrl": "https://github.com/world-sim-dev/zeus/actions/workflows/deploy.yml"
                }
              }
            }
          }'

      - name: OnFailure
        if: ${{ failure() }}
        run: |
          curl --request POST \
            --url ${{ secrets.FEISHU_WEBHOOK_URL }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus 构建失败 (${{ github.ref_name }})",
                  "theme": "red",
                  "content": "构建镜像：${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'

  build-zeus-openapi:
    if: ${{ inputs.build_openapi == true }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to ACR EE with the AccessKey pair
        uses: aliyun/acr-login@v1
        with:
          login-server: "https://${{ env.ACR_EE_REGISTRY }}"
          region-id: "${{ env.REGION_ID }}"
          access-key-id: "${{ secrets.aliyun_ak }}"
          access-key-secret: "${{ secrets.aliyun_sk}}"
          instance-id: "${{ env.ACR_EE_INSTANCE_ID }}"

      - name: Build and push image to ACR EE
        run: |
          docker build \
            -f Dockerfile-openapi \
            -t "$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_OPENAPI_IMAGE:$ACR_EE_TAG" .
          docker push "$ACR_EE_REGISTRY/$ACR_EE_NAMESPACE/$ACR_EE_OPENAPI_IMAGE:$ACR_EE_TAG"

      - name: OnSuccess
        if: ${{ success() }}
        run: |
          curl --request POST \
            --url ${{ secrets.feishu_webhook_url }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus OpenAPI 构建成功 (${{ github.ref_name }})",
                  "theme": "green",
                  "content": "构建镜像：${{ env.ACR_EE_TAG }}",
                  "btnText": "前往部署",
                  "detailUrl": "https://github.com/world-sim-dev/zeus/actions/workflows/deploy.yml"
                }
              }
            }
          }'

      - name: OnFailure
        if: ${{ failure() }}
        run: |
          curl --request POST \
            --url ${{ secrets.FEISHU_WEBHOOK_URL }} \
            --header 'content-type: application/json' \
            --data '{
            "msg_type": "interactive",
            "card": {
              "type": "template",
              "data": {
                "template_id": "AAqF23wD3EusT",
                "template_version_name": "1.0.2",
                "template_variable": {
                  "title": "Zeus OpenAPI 构建失败 (${{ github.ref_name }})",
                  "theme": "red",
                  "content": "构建镜像：${{ env.ACR_EE_TAG }}",
                  "btnText": "查看详情",
                  "detailUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                }
              }
            }
          }'